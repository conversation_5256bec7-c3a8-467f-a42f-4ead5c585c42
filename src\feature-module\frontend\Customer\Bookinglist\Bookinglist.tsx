import { useState, useCallback, useEffect, useMemo } from 'react';
import { useAuth } from 'react-oidc-context';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import RescheduleForm, { RescheduleData } from '../../../../feature-module/components/RescheduleForm/RescheduleForm';
import { useNavigate } from 'react-router-dom';
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Button, Chip, Divider, Spinner } from '@heroui/react';
import { FaEye, FaCalendarAlt, FaMapMarkerAlt, FaUser, FaEnvelope, FaPhone, FaDollarSign } from 'react-icons/fa';
import { apiClient } from '../../../../api';
import { createReview, CreateReviewData } from '../../../../service/reviewService';
import {
  getUserBookings,
  confirmBooking,
  finishBooking,
  cancelBooking,
  getBookingById,
  Booking as BookingType,
  BookingsResponse
} from '../../../../service/bookingService';
import { toast } from 'react-toastify';

// User data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  profileImage?: string;
}

const BookingList = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [showBookingDetails, setShowBookingDetails] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [bookingDetails, setBookingDetails] = useState<BookingType | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [userData, setUserData] = useState<UserData>({});
  const [userDataLoading, setUserDataLoading] = useState(false);
  const [bookingsLoading, setBookingsLoading] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);

  // Initial bookings data (fallback when API is not available)
  const initialBookings: Booking[] = useMemo(() => [
    {
      id: 1,
      service: 'Computer Services',
      status: 'Cancelled' as const,
      date: '27 Sep 2022, 17:00-18:00',
      amount: '$100.00',
      payment: 'PayPal',
      location: 'Newark, USA',
      provider: 'John Doe',
      email: '<EMAIL>',
      phone: '******-888-8888',
      actions: ['View Details', 'Reschedule'],
      providerId: 'provider-1',
      bookingId: 'booking-1',
    },
    {
      id: 2,
      service: 'Car Repair Services',
      status: 'Completed' as const,
      date: '23 Sep 2022, 10:00-11:00',
      amount: '$50.00',
      payment: 'COD',
      location: 'Alabama, USA',
      provider: 'John Smith',
      email: '<EMAIL>',
      phone: '******-275-5393',
      actions: ['View Details', 'Rebook', 'Add Review'],
      providerId: 'provider-2',
      bookingId: 'booking-2',
    },
    {
      id: 3,
      service: 'Interior Designing',
      status: 'Inprogress' as const,
      date: '22 Sep 2022, 11:00-12:00',
      amount: '$50.00',
      payment: 'PayPal',
      location: 'Washington, DC, USA',
      provider: 'Quentin',
      email: '<EMAIL>',
      phone: '******-810-9218',
      actions: ['View Details', 'Chat', 'Cancel'],
      providerId: 'provider-3',
      bookingId: 'booking-3',
    },
  ], []);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);

  // Fetch user data from API
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setUserDataLoading(true);
      console.log(`Fetching user data for ID: ${uid}`);

      const response = await apiClient.get(`/api/v1/user/${uid}`);

      if (response.data) {
        console.log('User data fetched successfully:', response.data);
        setUserData(response.data);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Fallback to auth profile data
      if (auth.user) {
        const fallbackData: UserData = {
          name: auth.user.profile.name ||
                auth.user.profile.given_name ||
                auth.user.profile.preferred_username ||
                auth.user.profile.email?.split('@')[0] ||
                'User',
          email: auth.user.profile.email,
          profileImage: 'https://via.placeholder.com/40'
        };
        setUserData(fallbackData);
      }
    } finally {
      setUserDataLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, auth.user]);

  // Fetch bookings data from API
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch bookings: No user ID or not authenticated');
      return;
    }

    try {
      setBookingsLoading(true);
      setBookingsError(null);
      console.log(`Fetching bookings for user ID: ${uid}`);

      const response = await getUserBookings({ userId: uid });

      if (response && response.bookings) {
        console.log('Bookings fetched successfully:', response.bookings);
        // Transform API data to match component interface
        const transformedBookings = response.bookings.map((booking: BookingType) => ({
          id: booking.id,
          service: booking.service,
          status: booking.status,
          date: booking.date,
          amount: booking.amount,
          payment: booking.payment,
          location: booking.location,
          provider: booking.provider,
          email: booking.email,
          phone: booking.phone,
          providerId: booking.providerId,
          bookingId: booking.bookingId || booking.id.toString(),
          serviceId: booking.serviceId,
          actions: getActionsForStatus(booking.status)
        }));
        setBookings(transformedBookings);
      } else {
        console.log('No bookings found, using fallback data');
        setBookings(initialBookings);
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setBookingsError('Failed to load bookings. Please try again.');
      // Fallback to initial bookings on error
      setBookings(initialBookings);
    } finally {
      setBookingsLoading(false);
    }
  }, [getUserId, auth.isAuthenticated, initialBookings]);

  // Helper function to determine actions based on booking status
  const getActionsForStatus = (status: string): string[] => {
    const baseActions = ['View Details'];
    switch (status) {
      case 'Pending':
        return [...baseActions, 'Confirm', 'Cancel'];
      case 'Confirmed':
        return [...baseActions, 'Review', 'Reschedule', 'Cancel'];
      case 'Finished':
        return [...baseActions, 'Rebook'];
      case 'Cancelled':
        return [...baseActions, 'Reschedule'];
      case 'Completed':
        return [...baseActions, 'Rebook', 'Add Review'];
      case 'Inprogress':
        return [...baseActions, 'Chat', 'Cancel'];
      case 'Rescheduled':
        return [...baseActions, 'Add to Calendar'];
      default:
        return [...baseActions, 'Chat'];
    }
  };

  // Fetch user data and bookings when component mounts
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchUserData();
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchUserData, fetchBookings]);

  // State to manage bookings
  const [bookings, setBookings] = useState<Booking[]>(initialBookings);

  type ButtonColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  type ButtonVariant = 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';

  // Define a type for booking (updated to match service interface)
  interface Booking {
    id: number | string;
    service: string;
    status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
    date: string;
    amount: string;
    payment: string;
    location: string;
    provider: string;
    email: string;
    phone: string;
    actions: string[];
    providerId?: string;
    bookingId?: string;
    serviceId?: string;
  }

  // Function to handle opening the review form
  const handleAddReview = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowReviewForm(true);
  };

  // Function to handle submitting a review
  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!selectedBooking) {
      console.error('No booking selected for review');
      return;
    }

    try {
      console.log('Review submitted:', reviewData);
      console.log('Image data in review:', {
        imageCount: reviewData.imageNames?.length || 0,
        imageNames: reviewData.imageNames,
        imageUrls: reviewData.imageUrls
      });

      // Prepare review data for API
      const createData: CreateReviewData = {
        providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
        serviceId: selectedBooking.id.toString(),
        serviceName: selectedBooking.service,
        bookingId: selectedBooking.bookingId || `booking-${selectedBooking.id}`,
        title: reviewData.title || selectedBooking.service,
        review: reviewData.review,
        comment: reviewData.review, // Backend expects comment field
        rating: reviewData.rating,
        images: reviewData.images,
        imageUrls: reviewData.imageUrls || [],
        imageNames: reviewData.imageNames || [], // Include image names for backend storage
      };

      console.log('Prepared review data for API:', {
        ...createData,
        imageNamesCount: createData.imageNames?.length || 0,
        imageNames: createData.imageNames,
        ratingFields: {
          rating: createData.rating,
          serviceRating: createData.serviceRating,
          qualityRating: createData.qualityRating,
          valueRating: createData.valueRating,
          communicationRating: createData.communicationRating,
          timelinessRating: createData.timelinessRating
        }
      });

      // Backend requires all rating fields - provide defaults using overall rating if not set
      createData.serviceRating = (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4)
        ? Math.round(reviewData.serviceRating)
        : Math.round(reviewData.rating);

      createData.qualityRating = (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4)
        ? Math.round(reviewData.qualityRating)
        : Math.round(reviewData.rating);

      createData.valueRating = (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4)
        ? Math.round(reviewData.valueRating)
        : Math.round(reviewData.rating);

      createData.communicationRating = (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4)
        ? Math.round(reviewData.communicationRating)
        : Math.round(reviewData.rating);

      createData.timelinessRating = (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4)
        ? Math.round(reviewData.timelinessRating)
        : Math.round(reviewData.rating);

      // Submit review to API
      await createReview(createData);

      // Update booking status to "Finished" after successful review submission
      if (selectedBooking.bookingId) {
        try {
          await finishBooking(selectedBooking.bookingId);
          console.log('Booking status updated to Finished');

          // Update local booking state
          setBookings(prevBookings =>
            prevBookings.map(booking =>
              booking.id === selectedBooking.id
                ? { ...booking, status: 'Finished', actions: ['Rebook'] }
                : booking
            )
          );
        } catch (statusError) {
          console.error('Error updating booking status:', statusError);
          // Don't fail the review submission if status update fails
        }
      }

      // Show success message
      toast.success('Review submitted successfully!');

      // Navigate to reviews page
      navigate('/customer/reviews');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    }
  };

  // Function to handle rescheduling
  const handleReschedule = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowRescheduleForm(true);
  };

  // Function to handle rescheduling submission
  const handleRescheduleSubmit = (rescheduleData: RescheduleData) => {
    try {
      // In a real app, you would send this data to your backend
      console.log('Reschedule data submitted:', rescheduleData);

      // Update the booking with the new date and time
      const updatedBookings = bookings.map(booking => {
        if (booking.id === rescheduleData.bookingId) {
          // Format the date
          const dateOptions: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          };
          const formattedDate = rescheduleData.newDate.toLocaleDateString('en-US', dateOptions);

          // Create the new date string
          const newDateString = `${formattedDate}, ${rescheduleData.newTime}`;

          // Update the booking status to "Rescheduled"
          return {
            ...booking,
            date: newDateString,
            status: 'Rescheduled' as const,
            // Add a notification action to allow user to add to calendar
            actions: [...booking.actions, 'Add to Calendar'].filter(action => action !== 'Reschedule'),
          };
        }
        return booking;
      });

      // Update the bookings state
      setBookings(updatedBookings);

      // Success is now handled in the RescheduleForm component with a visual notification
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      alert('Failed to reschedule booking. Please try again.');
    }
  };

  // Function to handle confirming a booking
  const handleConfirmBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      await confirmBooking(booking.bookingId);

      // Update local booking state
      setBookings(prevBookings =>
        prevBookings.map(b =>
          b.id === booking.id
            ? { ...b, status: 'Confirmed' as const, actions: ['Review', 'Reschedule', 'Cancel'] }
            : b
        )
      );

      toast.success('Booking confirmed successfully!');
    } catch (error) {
      console.error('Error confirming booking:', error);
      toast.error('Failed to confirm booking. Please try again.');
    }
  };

  // Function to handle cancelling a booking
  const handleCancelBooking = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    if (window.confirm('Are you sure you want to cancel this booking?')) {
      try {
        await cancelBooking(booking.bookingId);

        // Update local booking state
        setBookings(prevBookings =>
          prevBookings.map(b =>
            b.id === booking.id
              ? { ...b, status: 'Cancelled' as const, actions: ['View Details', 'Reschedule'] }
              : b
          )
        );

        toast.success('Booking cancelled successfully!');
      } catch (error) {
        console.error('Error cancelling booking:', error);
        toast.error('Failed to cancel booking. Please try again.');
      }
    }
  };

  // Function to handle viewing booking details
  const handleViewDetails = async (booking: Booking) => {
    if (!booking.bookingId) {
      toast.error('Booking ID not found');
      return;
    }

    try {
      setDetailsLoading(true);
      setSelectedBooking(booking);

      // Fetch detailed booking information
      const details = await getBookingById(booking.bookingId);
      setBookingDetails(details);
      setShowBookingDetails(true);
    } catch (error) {
      console.error('Error fetching booking details:', error);
      toast.error('Failed to load booking details. Please try again.');
    } finally {
      setDetailsLoading(false);
    }
  };

  // Function to handle button actions
  const handleButtonAction = (action: string, booking: Booking) => {
    switch (action) {
      case 'View Details':
        handleViewDetails(booking);
        break;
      case 'Confirm':
        handleConfirmBooking(booking);
        break;
      case 'Review':
      case 'Add Review':
        handleAddReview(booking);
        break;
      case 'Chat':
        navigate('/customer/chat');
        break;
      case 'Reschedule':
        handleReschedule(booking);
        break;
      case 'Cancel':
        handleCancelBooking(booking);
        break;
      case 'Rebook':
        // Handle rebook action
        alert('Rebook functionality will be implemented soon');
        break;
      case 'Add to Calendar': {
        // Create calendar event URL (works with Google Calendar)
        const eventTitle = encodeURIComponent(`Appointment: ${booking.service}`);
        const eventDetails = encodeURIComponent(`Provider: ${booking.provider}\nLocation: ${booking.location}\nContact: ${booking.email}, ${booking.phone}`);
        const eventLocation = encodeURIComponent(booking.location);

        // Parse date and time
        const [datePart, timePart] = booking.date.split(',');
        const dateObj = new Date(datePart);

        // Extract start and end times
        let startTime = '', endTime = '';
        if (timePart) {
          const timeRange = timePart.trim();
          const [start, end] = timeRange.split('-');
          startTime = start.trim();
          endTime = end ? end.trim() : '';
        }

        // Create start and end date objects
        const startDate = new Date(dateObj);
        const endDate = new Date(dateObj);

        // Set hours based on time string (simple parsing)
        if (startTime) {
          const [hourMin, period] = startTime.split(' ');
          let hour;
          const minute = hourMin.split(':').map(Number)[1] || 0;
          hour = Number(hourMin.split(':')[0]);
          if (period === 'PM' && hour < 12) hour += 12;
          if (period === 'AM' && hour === 12) hour = 0;
          startDate.setHours(hour, minute, 0);

          // Default end time is 1 hour later if not specified
          if (endTime) {
            const [endHourMin, endPeriod] = endTime.split(' ');
            let endHour;
            const endMinute = endHourMin.split(':').map(Number)[1] || 0;
            endHour = Number(endHourMin.split(':')[0]);
            if (endPeriod === 'PM' && endHour < 12) endHour += 12;
            if (endPeriod === 'AM' && endHour === 12) endHour = 0;
            endDate.setHours(endHour, endMinute, 0);
          } else {
            endDate.setHours(startDate.getHours() + 1, startDate.getMinutes(), 0);
          }
        }

        // Format dates for URL
        const formatDateForCalendar = (date: Date) => {
          return date.toISOString().replace(/-|:|\.\d+/g, '');
        };

        const startDateStr = formatDateForCalendar(startDate);
        const endDateStr = formatDateForCalendar(endDate);

        // Create Google Calendar URL
        const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${eventTitle}&details=${eventDetails}&location=${eventLocation}&dates=${startDateStr}/${endDateStr}`;

        // Open in new window
        window.open(calendarUrl, '_blank');
        break;
      }

      default:
        break;
    }
  };

  const getButtonStyles = (action: string): { color: ButtonColor; variant: ButtonVariant } => {
    switch (action) {
      case 'View Details':
        return {
          color: 'default',
          variant: 'bordered'
        };
      case 'Confirm':
        return {
          color: 'success',
          variant: 'solid'
        };
      case 'Review':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Cancel':
        return {
          color: 'danger',
          variant: 'light'
        };
      case 'Chat':
        return {
          color: 'success',
          variant: 'bordered'
        };
      case 'Reschedule':
        return {
          color: 'warning',
          variant: 'bordered'
        };
      case 'Add Review':
        return {
          color: 'secondary',
          variant: 'bordered'
        };
      case 'Rebook':
        return {
          color: 'primary',
          variant: 'solid'
        };
      case 'Add to Calendar':
        return {
          color: 'success',
          variant: 'flat'
        };
      default:
        return {
          color: 'primary',
          variant: 'bordered'
        };
    }
  };

  return (
    <div className="mx-auto p-4">
      <BreadCrumb title="Booking List" item1="Customer" />
      <h2 className="text-2xl sm mb-4 mt-4">Booking List</h2>

      {/* Loading State */}
      {bookingsLoading && (
        <div className="flex justify-center items-center py-8">
          <div className="text-lg text-gray-600">Loading bookings...</div>
        </div>
      )}

      {/* Error State */}
      {bookingsError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{bookingsError}</p>
          <button
            onClick={fetchBookings}
            className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
          >
            Retry
          </button>
        </div>
      )}

      {/* Bookings List */}
      {!bookingsLoading && !bookingsError && (
        <div className="space-y-6">
        {bookings.map((booking) => (
          <div
            key={booking.id}
            className="bg-white p-4 shadow-md rounded-md flex items-center"
          >
            {/* Placeholder for Image */}
            <div className="w-24 h-24 bg-gray-300 flex-shrink-0 rounded-md"></div>

            {/* Booking Details */}
            <div className="ml-4 flex-1">
              <h3 className="text-lg font-semibold mb-1">{booking.service}</h3>
              <span
                className={`text-sm px-2 py-1 rounded mb-2 inline-block ${
                  booking.status === 'Cancelled'
                    ? 'bg-red-200 text-red-700'
                    : booking.status === 'Completed'
                      ? 'bg-green-200 text-green-700'
                      : booking.status === 'Rescheduled'
                        ? 'bg-amber-200 text-amber-700'
                        : 'bg-blue-200 text-blue-700'
                }`}
              >
                {booking.status}
              </span>
              <p className="text-sm text-gray-600 mb-1">{booking.date}</p>
              <p className="text-sm mb-1">
                <span className="font-semibold">Amount:</span> {booking.amount}{' '}
                <span className="text-blue-600">{booking.payment}</span>
              </p>
              <p className="text-sm mb-1">
                <span className="font-semibold">Location:</span>{' '}
                {booking.location}
              </p>
              <p className="text-sm">
                <span className="font-semibold">Provider:</span>{' '}
                {booking.provider} -{' '}
                <a href={`mailto:${booking.email}`} className="text-blue-600">
                  {booking.email}
                </a>
                , {booking.phone}
              </p>
            </div>

            {/* Actions */}
            <div className="flex flex-col space-y-2">
              {booking.actions.map((action, index) => (
                <CustomButton
                  key={index}
                  label={action}
                  color={getButtonStyles(action).color}
                  variant={getButtonStyles(action).variant}
                  size="sm"
                  radius="sm"
                  className="min-w-[100px] font-medium"
                  onPress={() => handleButtonAction(action, booking)}
                />
              ))}
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Review Form Modal */}
      {selectedBooking && showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => setShowReviewForm(false)}
          onSubmit={handleSubmitReview}
          providerId={selectedBooking.providerId || `provider-${selectedBooking.id}`}
          serviceId={selectedBooking.id.toString()}
          bookingId={selectedBooking.bookingId || `booking-${selectedBooking.id}`}
          serviceName={selectedBooking.service}
          initialData={{
            id: `review-${Date.now()}`,
            providerId: selectedBooking.providerId || `provider-${selectedBooking.id}`,
            serviceId: selectedBooking.id.toString(),
            serviceName: selectedBooking.service,
            rating: 0,
            title: `Review for ${selectedBooking.service}`,
            review: '',
            images: [],
            imageUrls: [],
            imageNames: [] // Include empty image names array
          }}
        />
      )}

      {/* Reschedule Form Modal */}
      {selectedBooking && showRescheduleForm && (
        <RescheduleForm
          isOpen={showRescheduleForm}
          onClose={() => setShowRescheduleForm(false)}
          onSubmit={handleRescheduleSubmit}
          bookingDetails={{
            id: typeof selectedBooking.id === 'string' ? parseInt(selectedBooking.id) : selectedBooking.id,
            service: selectedBooking.service,
            date: selectedBooking.date,
            provider: selectedBooking.provider
          }}
        />
      )}

      {/* Booking Details Modal */}
      <Modal
        isOpen={showBookingDetails}
        onClose={() => {
          setShowBookingDetails(false);
          setBookingDetails(null);
          setSelectedBooking(null);
        }}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold">Booking Details</h2>
            {selectedBooking && (
              <p className="text-sm text-gray-600">Booking ID: {selectedBooking.bookingId || selectedBooking.id}</p>
            )}
          </ModalHeader>

          <ModalBody>
            {detailsLoading ? (
              <div className="flex justify-center items-center py-8">
                <Spinner size="lg" />
                <span className="ml-2">Loading booking details...</span>
              </div>
            ) : bookingDetails ? (
              <div className="space-y-6">
                {/* Status and Service Info */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold">{bookingDetails.service}</h3>
                    <Chip
                      color={
                        bookingDetails.status === 'Confirmed' ? 'success' :
                        bookingDetails.status === 'Pending' ? 'warning' :
                        bookingDetails.status === 'Cancelled' ? 'danger' :
                        bookingDetails.status === 'Finished' ? 'primary' :
                        'default'
                      }
                      variant="flat"
                    >
                      {bookingDetails.status}
                    </Chip>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <FaCalendarAlt className="text-blue-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Date & Time</p>
                        <p className="font-medium">{bookingDetails.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaDollarSign className="text-green-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Amount</p>
                        <p className="font-medium">{bookingDetails.amount}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaDollarSign className="text-purple-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Payment Method</p>
                        <p className="font-medium">{bookingDetails.payment}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaMapMarkerAlt className="text-red-500" />
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Location</p>
                        <p className="font-medium">{bookingDetails.location}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Divider />

                {/* Provider Information */}
                <div>
                  <h4 className="text-md font-semibold mb-3">Provider Information</h4>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <FaUser className="text-blue-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Provider Name</p>
                          <p className="font-medium">{bookingDetails.provider}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FaEnvelope className="text-green-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Email</p>
                          <p className="font-medium">{bookingDetails.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FaPhone className="text-purple-500" />
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Phone</p>
                          <p className="font-medium">{bookingDetails.phone}</p>
                        </div>
                      </div>
                      {bookingDetails.providerId && (
                        <div className="flex items-center space-x-2">
                          <FaEye className="text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-600 mb-1">Provider ID</p>
                            <p className="font-medium">{bookingDetails.providerId}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <Divider />

                {/* Booking Metadata */}
                <div>
                  <h4 className="text-md font-semibold mb-3">Booking Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {bookingDetails.serviceId && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Service ID</p>
                        <p className="font-medium">{bookingDetails.serviceId}</p>
                      </div>
                    )}
                    {bookingDetails.userId && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">User ID</p>
                        <p className="font-medium">{bookingDetails.userId}</p>
                      </div>
                    )}
                    {bookingDetails.createdAt && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Created At</p>
                        <p className="font-medium">{new Date(bookingDetails.createdAt).toLocaleString()}</p>
                      </div>
                    )}
                    {bookingDetails.updatedAt && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Last Updated</p>
                        <p className="font-medium">{new Date(bookingDetails.updatedAt).toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Available Actions */}
                {selectedBooking && selectedBooking.actions.length > 1 && (
                  <>
                    <Divider />
                    <div>
                      <h4 className="text-md font-semibold mb-3">Available Actions</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedBooking.actions
                          .filter(action => action !== 'View Details')
                          .map((action, index) => {
                            const styles = getButtonStyles(action);
                            return (
                              <Button
                                key={index}
                                color={styles.color}
                                variant={styles.variant}
                                size="sm"
                                onPress={() => {
                                  setShowBookingDetails(false);
                                  handleButtonAction(action, selectedBooking);
                                }}
                              >
                                {action}
                              </Button>
                            );
                          })}
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">Failed to load booking details.</p>
              </div>
            )}
          </ModalBody>

          <ModalFooter>
            <Button
              color="default"
              variant="light"
              onPress={() => {
                setShowBookingDetails(false);
                setBookingDetails(null);
                setSelectedBooking(null);
              }}
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default BookingList;
