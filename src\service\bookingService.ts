/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';

// Booking interfaces
export interface Booking {
  id: number | string;
  service: string;
  status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
  date: string;
  amount: string;
  payment: string;
  location: string;
  provider: string;
  email: string;
  phone: string;
  actions: string[];
  providerId?: string;
  bookingId?: string;
  serviceId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BookingsResponse {
  bookings: Booking[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UpdateBookingStatusData {
  status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
  updatedAt?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  userId?: string;
  status?: string;
}

/**
 * Get all bookings for a user
 */
export const getUserBookings = async (params: PaginationParams = {}): Promise<BookingsResponse> => {
  try {
    const { page = 1, limit = 10, userId, status } = params;
    console.log(`Fetching bookings - page: ${page}, limit: ${limit}, userId: ${userId}, status: ${status}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      queryParams.append('userId', userId);
    }

    if (status) {
      queryParams.append('status', status);
    }

    const response = await apiClient.get(`/api/v1/booking?${queryParams.toString()}`);
    console.log('Bookings fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching bookings:', error);
    logger.error('Error fetching bookings:', error);
    throw error;
  }
};

/**
 * Get a specific booking by ID
 */
export const getBookingById = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Fetching booking with ID: ${bookingId}`);
    const response = await apiClient.get(`/api/v1/booking/${bookingId}`);
    console.log('Booking fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching booking ${bookingId}:`, error);
    logger.error(`Error fetching booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Update booking status
 */
export const updateBookingStatus = async (bookingId: string, data: UpdateBookingStatusData): Promise<Booking> => {
  try {
    console.log(`Updating booking ${bookingId} status to:`, data.status);

    const updatePayload = {
      ...data,
      updatedAt: data.updatedAt || new Date().toISOString(),
    };

    const response = await apiClient.put(`/api/v1/booking/${bookingId}`, updatePayload);
    console.log('Booking status updated successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating booking ${bookingId} status:`, error);
    logger.error(`Error updating booking ${bookingId} status:`, error);
    throw error;
  }
};

/**
 * Confirm a booking (change status from Pending to Confirmed)
 */
export const confirmBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Confirming booking: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Confirmed' });
  } catch (error: any) {
    console.error(`Error confirming booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Mark booking as finished (typically after review submission)
 */
export const finishBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Marking booking as finished: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Finished' });
  } catch (error: any) {
    console.error(`Error finishing booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Cancel a booking
 */
export const cancelBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Cancelling booking: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Cancelled' });
  } catch (error: any) {
    console.error(`Error cancelling booking ${bookingId}:`, error);
    throw error;
  }
};

export const getStaffById = async (id: string) => {
  try {
    const res = await apiClient.get(
      `${import.meta.env.VITE_APP_BACKEND_SERVICE}/${Path.getStaff}` + `/${id}`
    );
    return res.data;
  } catch (error: unknown) {
    console.log('ERROR: ', error);
    // logger.error(error);
    throw error;
  }
};
